// Firebase Firestore 보안 규칙 - App Check 호환 버전

rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // 사용자 인증 확인 함수
    function isAuthenticated() {
      return request.auth != null;
    }

    // 사용자 소유권 확인 함수
    function isOwner(userId) {
      return request.auth.uid == userId;
    }

    // App Check 토큰 확인 (임시 비활성화)
    function isAppCheckValid() {
      // 개발 중에는 App Check 검증을 완전히 우회
      return true;
    }

    // 행사 접근 권한 확인 함수 (소유자 또는 초대받은 사용자)
    function hasEventAccess(userId, eventId) {
      return isOwner(userId) ||
             exists(/databases/$(database)/documents/event_permissions/$(eventId)/users/$(request.auth.uid));
    }

    // 행사 수정 권한 확인 함수 (소유자만 가능)
    function canModifyEvent(userId, eventId) {
      return isOwner(userId);
    }
    
    // 사용자 문서에 대한 규칙
    match /users/{userId} {
      // 읽기/수정: 본인만 가능
      allow read, update: if isAuthenticated() && isOwner(userId);
      // 생성: 인증된 사용자가 본인 문서 생성 시 허용
      allow create: if isAuthenticated() && request.auth.uid == userId;

      // 사용자 설정 하위 컬렉션
      match /settings/{settingId} {
        allow read, write: if isAuthenticated() && isOwner(userId);
      }

      // 이벤트 하위 컬렉션
      match /events/{eventId} {
        // 읽기: 소유자 또는 초대받은 사용자
        allow read: if isAuthenticated() && hasEventAccess(userId, eventId);
        // 쓰기: 소유자만 가능
        allow write: if isAuthenticated() && canModifyEvent(userId, eventId);

        // 상품 하위 컬렉션
        match /products/{productId} {
          allow read: if isAuthenticated() && hasEventAccess(userId, eventId) && isAppCheckValid();
          allow write: if isAuthenticated() && canModifyEvent(userId, eventId) && isAppCheckValid();
        }

        // 가상 상품 하위 컬렉션
        match /virtual_products/{productId} {
          allow read: if isAuthenticated() && hasEventAccess(userId, eventId) && isAppCheckValid();
          allow write: if isAuthenticated() && canModifyEvent(userId, eventId) && isAppCheckValid();
        }

        // 상품 링크 하위 컬렉션
        match /product_links/{linkId} {
          allow read: if isAuthenticated() && hasEventAccess(userId, eventId) && isAppCheckValid();
          allow write: if isAuthenticated() && canModifyEvent(userId, eventId) && isAppCheckValid();
        }

        // 판매자 하위 컬렉션
        match /sellers/{sellerId} {
          allow read: if isAuthenticated() && hasEventAccess(userId, eventId) && isAppCheckValid();
          allow write: if isAuthenticated() && canModifyEvent(userId, eventId) && isAppCheckValid();
        }

        // 선입금 하위 컬렉션
        match /prepayments/{prepaymentId} {
          allow read: if isAuthenticated() && hasEventAccess(userId, eventId) && isAppCheckValid();
          // 선입금은 초대받은 사용자도 수령/미수령 상태 변경 가능
          allow write: if isAuthenticated() && hasEventAccess(userId, eventId) && isAppCheckValid();
        }

        // 판매 로그 하위 컬렉션
        match /sales_logs/{logId} {
          allow read: if isAuthenticated() && hasEventAccess(userId, eventId) && isAppCheckValid();
          allow write: if isAuthenticated() && canModifyEvent(userId, eventId) && isAppCheckValid();
        }

        // 할인 설정 하위 컬렉션
        match /set_discounts/{discountId} {
          allow read: if isAuthenticated() && hasEventAccess(userId, eventId) && isAppCheckValid();
          allow write: if isAuthenticated() && canModifyEvent(userId, eventId) && isAppCheckValid();
        }

        // 세트 할인 거래 하위 컬렉션
        match /set_discount_transactions/{transactionId} {
          allow read: if isAuthenticated() && hasEventAccess(userId, eventId) && isAppCheckValid();
          allow write: if isAuthenticated() && canModifyEvent(userId, eventId) && isAppCheckValid();
        }
      }
    }

    // 초대 관련 컬렉션
    match /event_invitations/{invitationId} {
      // 초대 생성: 인증된 사용자만
      allow create: if isAuthenticated() && isAppCheckValid();
      // 초대 읽기: 모든 인증된 사용자 (코드 검증용)
      allow read: if isAuthenticated() && isAppCheckValid();
      // 초대 수정: 초대 생성자 또는 초대받은 사용자
      allow update: if isAuthenticated() && isAppCheckValid() &&
                   (resource.data.ownerUserId == request.auth.uid ||
                    resource.data.invitedUserId == request.auth.uid);
      // 초대 삭제: 초대 생성자만
      allow delete: if isAuthenticated() && isAppCheckValid() &&
                   resource.data.ownerUserId == request.auth.uid;
    }

    // 권한 관리 컬렉션
    match /event_permissions/{eventId} {
      match /users/{userId} {
        // 권한 읽기: 해당 행사의 소유자 또는 본인
        allow read: if isAuthenticated() && isAppCheckValid() &&
                   (exists(/databases/$(database)/documents/users/$(request.auth.uid)/events/$(eventId)) ||
                    userId == request.auth.uid);
        // 권한 생성/수정: 해당 행사의 소유자만
        allow create, update: if isAuthenticated() && isAppCheckValid() &&
                             exists(/databases/$(database)/documents/users/$(request.auth.uid)/events/$(eventId));
        // 권한 삭제: 해당 행사의 소유자 또는 본인
        allow delete: if isAuthenticated() && isAppCheckValid() &&
                     (exists(/databases/$(database)/documents/users/$(request.auth.uid)/events/$(eventId)) ||
                      userId == request.auth.uid);
      }
    }
  }
}
