import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_auth/firebase_auth.dart';

import '../../models/event.dart';
import '../../models/event_state.dart';
// 정렬 기능 제거: event_sort_option import 삭제
import '../../providers/event_provider.dart';
import '../../providers/unified_workspace_provider.dart';
import '../../utils/event_workspace_utils.dart';
import '../../providers/product_provider.dart';
import '../../providers/prepayment_provider.dart';
import '../../utils/logger_utils.dart';
import '../../widgets/loading_indicator.dart';
import '../../widgets/confirmation_dialog.dart';
import '../../widgets/event_image.dart';
import 'event_form_screen.dart';
import '../../widgets/app_bar_styles.dart';
import '../../providers/invitation_provider.dart';
import '../../providers/nickname_provider.dart';
import '../../providers/permission_provider.dart';
import '../../models/event_invitation.dart';
import '../../models/invitation_status.dart';
import '../../utils/toast_utils.dart';
import 'package:share_plus/share_plus.dart';

/// 행사 목록 화면
/// 
/// 사용자가 제시한 DAILY TRIP 앱과 유사한 디자인으로 구현
/// - 행사 목록 표시
/// - 행사 선택 기능
/// - 행사 추가/수정/삭제 기능
class EventListScreen extends ConsumerStatefulWidget {
  const EventListScreen({super.key});

  @override
  ConsumerState<EventListScreen> createState() => _EventListScreenState();
}

class _EventListScreenState extends ConsumerState<EventListScreen> {
  static const String _tag = 'EventListScreen';
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    LoggerUtils.methodStart('initState', tag: _tag);
    
    // 화면 진입 시 행사 목록 로드
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(eventNotifierProvider.notifier).loadEvents();
    });
    
    LoggerUtils.methodEnd('initState', tag: _tag);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final eventState = ref.watch(eventNotifierProvider);
    final workspaceState = ref.watch(unifiedWorkspaceProvider); // 워크스페이스 상태 추가
    final currentWorkspace = ref.watch(currentWorkspaceProvider);
    final currentEvent = EventWorkspaceUtils.workspaceToEvent(currentWorkspace);

    return Scaffold(
      appBar: _buildAppBar(),
      body: Stack(
        children: [
          _buildBody(eventState, currentEvent),
          // 워크스페이스 전환 로딩 오버레이
          if (workspaceState.isLoading) _buildWorkspaceLoadingOverlay(workspaceState),
        ],
      ),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  /// AppBar 구성
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Builder(builder: (ctx)=> Text('행사 관리', style: AppBarStyles.of(ctx))),
      centerTitle: true,
      backgroundColor: Theme.of(context).colorScheme.primary,
      elevation: 0,
      iconTheme: IconThemeData(color: Theme.of(context).colorScheme.onPrimary),
  // 정렬 버튼 제거됨
    );
  }

  /// 메인 바디 구성
  Widget _buildBody(EventState eventState, Event? currentEvent) {
    if (eventState.isLoading && eventState.events.isEmpty) {
      return const Center(child: LoadingIndicator());
    }

    if (eventState.hasError) {
      return _buildErrorWidget(eventState.errorMessage!);
    }

    if (eventState.isEmpty) {
      return _buildEmptyWidget();
    }

    return RefreshIndicator(
      onRefresh: () => ref.read(eventNotifierProvider.notifier).refresh(),
      child: Column(
        children: [
          // 검색바
          _buildSearchBar(),
          
          // 행사 목록
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: eventState.events.length,
              itemExtent: 120.0, // 고정 높이로 스크롤 성능 최적화 (EventCard 전체 높이)
              itemBuilder: (context, index) {
                final event = eventState.events[index];
                final isSelected = currentEvent?.id == event.id;
                return _buildEventCard(event, isSelected);
              },
            ),
          ),
        ],
      ),
    );
  }

  /// 검색바 구성
  Widget _buildSearchBar() {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextField(
        controller: _searchController,
        decoration: const InputDecoration(
          hintText: '행사 검색...',
          prefixIcon: Icon(Icons.search, color: Colors.grey),
          border: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        ),
        onChanged: (value) {
          ref.read(eventNotifierProvider.notifier).searchEvents(value);
        },
      ),
    );
  }

  /// 행사 카드 구성 (DAILY TRIP 스타일)
  Widget _buildEventCard(Event event, bool isSelected) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: isSelected 
          ? Border.all(color: Theme.of(context).primaryColor, width: 2)
          : null,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () => _selectEvent(event),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // 행사 이미지 또는 아이콘
              _buildEventImage(event),
              
              const SizedBox(width: 16),
              
              // 행사 정보
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 행사명과 상태 표시
                    Row(
                      children: [
                        Expanded(
                          child: FutureBuilder<bool>(
                            future: ref.read(permissionNotifierProvider.notifier).isInvitedUser(event.id!),
                            builder: (context, snapshot) {
                              final isInvited = snapshot.data ?? false;

                              if (isInvited) {
                                return FutureBuilder<List<EventInvitation>>(
                                  future: ref.read(eventInvitationRepositoryProvider).getInvitationsByEvent(event.id!),
                                  builder: (context, invitationSnapshot) {
                                    final invitations = invitationSnapshot.data ?? [];
                                    final acceptedInvitation = invitations.where((inv) => inv.status == InvitationStatus.accepted).firstOrNull;
                                    final ownerNickname = acceptedInvitation?.ownerNickname ?? '알 수 없는 사용자';

                                    return Text(
                                      '${event.name} - $ownerNickname님의 행사',
                                      style: const TextStyle(
                                        fontFamily: 'Pretendard',
                                        fontSize: 16,
                                        fontWeight: FontWeight.w600,
                                        color: Colors.black,
                                      ),
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    );
                                  },
                                );
                              } else {
                                return Text(
                                  event.name,
                                  style: const TextStyle(
                                    fontFamily: 'Pretendard',
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.black,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                );
                              }
                            },
                          ),
                        ),
                        // 초대받은 행사 아이콘
                        FutureBuilder<bool>(
                          future: ref.read(permissionNotifierProvider.notifier).isInvitedUser(event.id!),
                          builder: (context, snapshot) {
                            final isInvited = snapshot.data ?? false;
                            if (isInvited) {
                              return Container(
                                margin: const EdgeInsets.only(right: 8),
                                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                decoration: BoxDecoration(
                                  color: Colors.orange.withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: const Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(Icons.group, size: 12, color: Colors.orange),
                                    SizedBox(width: 2),
                                    Text(
                                      '초대됨',
                                      style: TextStyle(
                                        fontSize: 10,
                                        color: Colors.orange,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            }
                            return const SizedBox.shrink();
                          },
                        ),
                        // 상태 표시
                        _buildStatusChip(event),
                      ],
                    ),
                    
                    const SizedBox(height: 4),
                    
                    // 날짜 정보
                    Text(
                      event.dateRangeString,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
              
              // 더보기 메뉴
              FutureBuilder<bool>(
                future: ref.read(permissionNotifierProvider.notifier).isEventOwner(event.id!),
                builder: (context, snapshot) {
                  final isOwner = snapshot.data ?? true; // 기본값은 소유자로 가정

                  return PopupMenuButton<String>(
                    icon: const Icon(Icons.more_vert, color: Colors.grey),
                    onSelected: (value) => _handleMenuAction(value, event),
                    itemBuilder: (context) => [
                      if (isOwner) ...[
                        const PopupMenuItem(
                          value: 'edit',
                          child: Row(
                            children: [
                              Icon(Icons.edit, size: 20),
                              SizedBox(width: 8),
                              Text('수정'),
                            ],
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'invite',
                          child: Row(
                            children: [
                              Icon(Icons.group_add, size: 20),
                              SizedBox(width: 8),
                              Text('행사 초대'),
                            ],
                          ),
                        ),
                        // 행사가 1개만 있을 때는 삭제 불가
                        if (ref.watch(eventNotifierProvider).events.length > 1)
                          const PopupMenuItem(
                            value: 'delete',
                            child: Row(
                              children: [
                                Icon(Icons.delete, size: 20, color: Colors.red),
                                SizedBox(width: 8),
                                Text('삭제', style: TextStyle(color: Colors.red)),
                              ],
                            ),
                          ),
                      ] else ...[
                        // 초대받은 사용자는 나가기만 가능
                        const PopupMenuItem(
                          value: 'leave',
                          child: Row(
                            children: [
                              Icon(Icons.exit_to_app, size: 20, color: Colors.orange),
                              SizedBox(width: 8),
                              Text('나가기', style: TextStyle(color: Colors.orange)),
                            ],
                          ),
                        ),
                      ],
                    ],
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 행사 이미지 구성
  Widget _buildEventImage(Event event) {
    return Container(
      width: 50,
      height: 50,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: Colors.grey[200],
      ),
      child: EventImage(
        imagePath: event.imagePath,
        width: 50,
        height: 50,
        fit: BoxFit.cover,
        borderRadius: BorderRadius.circular(8),
      ),
    );
  }

  /// 상태 칩 구성
  Widget _buildStatusChip(Event event) {
    Color chipColor;
    String statusText;
    
    if (event.isOngoing) {
      chipColor = Colors.green;
      statusText = '진행중';
    } else if (event.isUpcoming) {
      chipColor = Colors.orange;
      statusText = '예정';
    } else {
      chipColor = Colors.grey;
      statusText = '종료';
    }
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: chipColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: chipColor.withValues(alpha: 0.3)),
      ),
      child: Text(
        statusText,
        style: TextStyle(
          color: chipColor,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  /// 에러 위젯
  Widget _buildErrorWidget(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            '오류가 발생했습니다',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              ref.read(eventNotifierProvider.notifier).refresh();
            },
            child: const Text('다시 시도'),
          ),
        ],
      ),
    );
  }

  /// 빈 상태 위젯
  Widget _buildEmptyWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.event_note,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            '등록된 행사가 없습니다',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '새로운 행사를 추가해보세요',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _addEvent,
            icon: const Icon(Icons.add),
            label: const Text('행사 추가'),
          ),
        ],
      ),
    );
  }

  /// FloatingActionButton 구성
  Widget _buildFloatingActionButton() {
    return FloatingActionButton(
      onPressed: _addEvent,
      child: const Icon(Icons.add),
    );
  }

  /// 행사 선택
  void _selectEvent(Event event) async {
    try {
      LoggerUtils.methodStart('_selectEvent', tag: _tag, data: {'eventId': event.id});

      await ref.read(eventNotifierProvider.notifier).setCurrentEvent(event);

      // 행사 선택 후 관련 데이터 새로고침
      await _refreshDataAfterEventChange();

      // 현재 행사 설정이 완료되었는지 확인
      final currentWorkspace = ref.read(currentWorkspaceProvider);
      final currentEvent = EventWorkspaceUtils.workspaceToEvent(currentWorkspace);
      if (currentEvent?.id == event.id) {
        LoggerUtils.logInfo('행사 선택 완료: ${event.name}', tag: _tag);
      } else {
        LoggerUtils.logWarning('행사 선택 후 현재 행사 설정 확인 실패', tag: _tag);
      }

      if (mounted) {
        Navigator.of(context).pop(); // 행사 선택 후 이전 화면으로 돌아가기

        // 추가적으로 메인 화면 강제 갱신을 위해 약간의 지연 후 상태 변경 알림
        Future.delayed(const Duration(milliseconds: 100), () {
          if (mounted) {
            // 현재 행사 변경을 다시 한 번 알림 (UI 갱신 보장)
            final eventWorkspace = EventWorkspaceUtils.eventToWorkspace(event);
            if (eventWorkspace != null) {
              ref.read(unifiedWorkspaceProvider.notifier).switchToWorkspace(eventWorkspace);
            }
          }
        });
      }

      LoggerUtils.methodEnd('_selectEvent', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('행사 선택 실패', tag: _tag, error: e);
    }
  }

  /// 행사 변경 후 관련 데이터 새로고침
  Future<void> _refreshDataAfterEventChange() async {
    try {
      LoggerUtils.methodStart('_refreshDataAfterEventChange', tag: _tag);

      // 핵심 수정: 에러 상태 클리어 후 데이터 로딩
      final productNotifier = ref.read(productNotifierProvider.notifier);
      final prepaymentNotifier = ref.read(prepaymentNotifierProvider.notifier);

      // 에러 상태 클리어
      productNotifier.clearError();
      prepaymentNotifier.clearError();

      // 데이터 새로고침
      await productNotifier.loadProducts(showLoading: false);
      await prepaymentNotifier.loadPrepayments(showLoading: false);

      LoggerUtils.methodEnd('_refreshDataAfterEventChange', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('행사 변경 후 데이터 새로고침 실패', tag: _tag, error: e);
    }
  }

  /// 행사 추가
  void _addEvent() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const EventFormScreen(),
      ),
    );
  }

  /// 메뉴 액션 처리
  void _handleMenuAction(String action, Event event) {
    switch (action) {
      case 'edit':
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => EventFormScreen(event: event),
          ),
        );
        break;
      case 'invite':
        _showInviteDialog(event);
        break;
      case 'leave':
        _showLeaveConfirmation(event);
        break;
      case 'delete':
        _showDeleteConfirmation(event);
        break;
    }
  }

  /// 행사 초대 다이얼로그
  void _showInviteDialog(Event event) {
    showDialog<void>(
      context: context,
      builder: (context) => _InviteDialog(event: event),
    );
  }

  /// 행사 나가기 확인 다이얼로그
  void _showLeaveConfirmation(Event event) {
    showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('행사 나가기'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('${event.name} 행사에서 나가시겠습니까?'),
            const SizedBox(height: 8),
            const Text(
              '나가기 후에는 다시 초대받아야 참여할 수 있습니다.',
              style: TextStyle(color: Colors.grey, fontSize: 12),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('취소'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _leaveEvent(event);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
            ),
            child: const Text('나가기'),
          ),
        ],
      ),
    );
  }

  /// 행사 나가기 처리
  Future<void> _leaveEvent(Event event) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        ToastUtils.showError(context, '로그인이 필요합니다');
        return;
      }

      // 권한 제거
      await ref.read(permissionNotifierProvider.notifier)
          .revokePermission(event.id!, user.uid);

      // 행사 목록 새로고침
      await ref.read(eventNotifierProvider.notifier).refresh();

      // 남은 행사가 없으면 온보딩으로 이동
      final events = ref.read(eventNotifierProvider).events;
      if (events.isEmpty) {
        if (mounted) {
          ToastUtils.showWarning(context, '참여 중인 행사가 없습니다. 새 행사를 생성하거나 초대받아 참여하세요.');
          // 온보딩 화면으로 이동하는 로직은 앱의 라우팅 구조에 따라 구현
          Navigator.of(context).pushNamedAndRemoveUntil('/', (route) => false);
        }
      } else {
        ToastUtils.showSuccess(context, '${event.name} 행사에서 나갔습니다');
      }
    } catch (e) {
      ToastUtils.showError(context, '행사 나가기 실패: ${e.toString()}');
    }
  }

  /// 삭제 확인 다이얼로그
  void _showDeleteConfirmation(Event event) {
    ConfirmationDialog.showDelete(
      context: context,
      title: '행사 삭제',
      message: '${event.name} 행사를 삭제하시겠습니까?\n\n관련된 모든 데이터가 함께 삭제됩니다.',
      confirmLabel: '삭제',
      cancelLabel: '취소',
      onConfirm: () {
        ref.read(eventNotifierProvider.notifier).deleteEvent(event.id!);
      },
    );
  }

  // 정렬 옵션 UI/로직 제거
  /// 워크스페이스 전환 로딩 오버레이
  Widget _buildWorkspaceLoadingOverlay(UnifiedWorkspaceState workspaceState) {
    return Container(
      color: Colors.black.withValues(alpha: 0.5),
      child: Center(
        child: Card(
          margin: const EdgeInsets.all(32),
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const CircularProgressIndicator(),
                const SizedBox(height: 16),
                Text(
                  workspaceState.loadingMessage ?? '행사 전환 중...',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                ),
                if (workspaceState.errorMessage != null) ...[
                  const SizedBox(height: 12),
                  Text(
                    workspaceState.errorMessage!,
                    style: TextStyle(
                      fontSize: 14,
                      color: Theme.of(context).colorScheme.error,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// 행사 초대 다이얼로그
class _InviteDialog extends ConsumerStatefulWidget {
  final Event event;

  const _InviteDialog({required this.event});

  @override
  ConsumerState<_InviteDialog> createState() => _InviteDialogState();
}

class _InviteDialogState extends ConsumerState<_InviteDialog> {
  String? _invitationCode;
  bool _isGenerating = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _checkExistingInvitation();
  }

  /// 기존 초대 코드 확인
  Future<void> _checkExistingInvitation() async {
    try {
      final existingInvitations = await ref.read(eventInvitationRepositoryProvider)
          .getInvitationsByEvent(widget.event.id!);

      // 만료되지 않은 초대 코드 찾기
      final validInvitation = existingInvitations
          .where((inv) => inv.expiresAt.isAfter(DateTime.now()))
          .firstOrNull;

      if (validInvitation != null && mounted) {
        setState(() {
          _invitationCode = validInvitation.invitationCode;
        });
      }
    } catch (e) {
      // 에러는 무시하고 새로 생성할 수 있도록 함
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('${widget.event.name} 초대'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('다른 사용자를 이 행사에 초대하세요.'),
          const SizedBox(height: 16),
          if (_invitationCode != null) ...[
            const Text('초대 코드:', style: TextStyle(fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      _invitationCode!,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        letterSpacing: 2,
                      ),
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.copy),
                    onPressed: () => _copyToClipboard(_invitationCode!),
                    tooltip: '복사',
                  ),
                ],
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              '이 코드는 2시간 후 만료됩니다.',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
          if (_errorMessage != null) ...[
            const SizedBox(height: 8),
            Text(
              _errorMessage!,
              style: const TextStyle(color: Colors.red),
            ),
          ],
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('닫기'),
        ),
        if (_invitationCode == null)
          ElevatedButton(
            onPressed: _isGenerating ? null : _generateInvitationCode,
            child: _isGenerating
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('초대 코드 생성'),
          )
        else ...[
          ElevatedButton.icon(
            onPressed: () => _shareInvitationCode(_invitationCode!),
            icon: const Icon(Icons.share),
            label: const Text('공유'),
          ),
        ],
      ],
    );
  }

  /// 초대 코드 생성 또는 기존 코드 재사용
  Future<void> _generateInvitationCode() async {
    setState(() {
      _isGenerating = true;
      _errorMessage = null;
    });

    try {
      final nickname = ref.read(nicknameProvider);
      if (nickname?.name == null) {
        throw Exception('닉네임이 설정되지 않았습니다');
      }

      // 먼저 기존 유효한 초대 코드 확인
      final existingInvitations = await ref.read(eventInvitationRepositoryProvider)
          .getInvitationsByEvent(widget.event.id!);

      final validInvitation = existingInvitations
          .where((inv) => inv.expiresAt.isAfter(DateTime.now()))
          .firstOrNull;

      String invitationCode;
      if (validInvitation != null) {
        // 기존 코드 재사용
        invitationCode = validInvitation.invitationCode;
      } else {
        // 새 코드 생성
        final invitation = await ref.read(invitationNotifierProvider.notifier)
            .createInvitation(
              eventId: widget.event.id!,
              eventName: widget.event.name,
              ownerNickname: nickname!.name,
            );
        invitationCode = invitation.invitationCode;
      }

      setState(() {
        _invitationCode = invitationCode;
        _isGenerating = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = '초대 코드 생성 실패: ${e.toString()}';
        _isGenerating = false;
      });
    }
  }

  /// 클립보드에 복사
  Future<void> _copyToClipboard(String code) async {
    await Clipboard.setData(ClipboardData(text: code));
    if (mounted) {
      ToastUtils.showSuccess(context, '초대 코드가 복사되었습니다');
    }
  }

  /// 초대 코드 공유
  Future<void> _shareInvitationCode(String code) async {
    final text = '${widget.event.name} 행사에 초대합니다!\n\n'
        '바라 부스 매니저 앱에서 다음 초대 코드를 입력하세요:\n'
        '$code\n\n'
        '이 코드는 2시간 후 만료됩니다.';

    await Share.share(text);
  }
}

